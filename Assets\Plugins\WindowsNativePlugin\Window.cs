using System;
using System.Runtime.InteropServices;
using UnityEngine;

namespace WindowsNativePlugin
{
    public static class Window
    {
        #region DLL导入

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowFullscreen();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowMaximize();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowNormal();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowMinimize();

        [DllImport("WindowsNativePlugin")]
        private static extern void RestoreWindow();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowStyleFramed();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowStyleFramedWithoutTitleBar();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowStyleFramedWithoutButtons();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowStyleFrameless();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsWindowStyleFramed();

        // 添加圆角无边框窗口支持
        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowStyleRoundedFrameless(int cornerRadius);

        [DllImport("WindowsNativePlugin")]
        private static extern int GetWindowCornerRadius();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsWindowStyleRoundedFrameless();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsWindowStyleFramedWithoutTitleBar();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsWindowStyleFramedWithoutButtons();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsWindowStyleFrameless();

        [DllImport("WindowsNativePlugin")]
        private static extern void EnableCloseButton();

        [DllImport("WindowsNativePlugin")]
        private static extern void DisableCloseButton();

        [DllImport("WindowsNativePlugin")]
        private static extern void EnableMaximizeButton();

        [DllImport("WindowsNativePlugin")]
        private static extern void DisableMaximizeButton();

        [DllImport("WindowsNativePlugin")]
        private static extern void EnableMinimizeButton();

        [DllImport("WindowsNativePlugin")]
        private static extern void DisableMinimizeButton();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsCloseButtonEnabled();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsMaximizeButtonEnabled();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetIsMinimizeButtonEnabled();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowPosition(int x, int y);

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowSize(int width, int height);

        [DllImport("WindowsNativePlugin")]
        private static extern void GetWindowPosition(out int x, out int y);

        [DllImport("WindowsNativePlugin")]
        private static extern void GetWindowSize(out int width, out int height);

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowTitle(string title);

        [DllImport("WindowsNativePlugin")]
        private static extern IntPtr GetWindowTitle();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowAlpha(byte alpha);

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetWindowHasAlpha();

        [DllImport("WindowsNativePlugin")]
        private static extern float GetWindowOpacity();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetWindowOpacity(float opacity);

        /// <summary>
        /// 获取窗口透明度（0-1范围）
        /// </summary>
        public static float GetOpacity()
        {
            return GetWindowOpacity();
        }

        /// <summary>
        /// 设置窗口透明度（0-1范围）
        /// </summary>
        /// <param name="opacity">透明度值（0表示完全透明，1表示完全不透明）</param>
        public static void SetOpacity(float opacity)
        {
            SetWindowOpacity(opacity);
        }

        #endregion

        #region 公共API

        /// <summary>
        /// 将窗口设置为全屏模式
        /// </summary>
        public static void StateFullscreen()
        {
            SetWindowFullscreen();
        }

        /// <summary>
        /// 将窗口最大化
        /// </summary>
        public static void StateMaximize()
        {
            SetWindowMaximize();
        }

        /// <summary>
        /// 将窗口设置为正常状态
        /// </summary>
        public static void StateNormal()
        {
            SetWindowNormal();
        }

        /// <summary>
        /// 将窗口最小化
        /// </summary>
        public static void StateMinimize()
        {
            SetWindowMinimize();
        }

        /// <summary>
        /// 恢复窗口状态
        /// </summary>
        public static void StateRestore()
        {
            RestoreWindow();
        }

        /// <summary>
        /// 设置窗口为有边框样式
        /// </summary>
        public static void StyleFramed()
        {
            SetWindowStyleFramed();
        }

        /// <summary>
        /// 设置窗口为无标题栏的有边框样式
        /// </summary>
        public static void StyleFramedWithoutTitleBar()
        {
            SetWindowStyleFramedWithoutTitleBar();
        }

        /// <summary>
        /// 设置窗口为无按钮的有边框样式
        /// </summary>
        public static void StyleFramedWithoutButtons()
        {
            SetWindowStyleFramedWithoutButtons();
        }

        /// <summary>
        /// 设置窗口为无边框样式
        /// </summary>
        public static void StyleFrameless()
        {
            SetWindowStyleFrameless();
        }

        /// <summary>
        /// 检查窗口是否为有边框样式
        /// </summary>
        public static bool IsStyleFramed()
        {
            return GetIsWindowStyleFramed();
        }

        /// <summary>
        /// 检查窗口是否为无标题栏的有边框样式
        /// </summary>
        public static bool IsStyleFramedWithoutTitleBar()
        {
            return GetIsWindowStyleFramedWithoutTitleBar();
        }

        /// <summary>
        /// 检查窗口是否为无按钮的有边框样式
        /// </summary>
        public static bool IsStyleFramedWithoutButtons()
        {
            return GetIsWindowStyleFramedWithoutButtons();
        }

        /// <summary>
        /// 检查窗口是否为无边框样式
        /// </summary>
        public static bool IsStyleFrameless()
        {
            return GetIsWindowStyleFrameless();
        }

        /// <summary>
        /// 启用关闭按钮
        /// </summary>
        public static void ButtonEnableClose()
        {
            EnableCloseButton();
        }

        /// <summary>
        /// 禁用关闭按钮
        /// </summary>
        public static void ButtonDisableClose()
        {
            DisableCloseButton();
        }

        /// <summary>
        /// 启用最大化按钮
        /// </summary>
        public static void ButtonEnableMaximize()
        {
            EnableMaximizeButton();
        }

        /// <summary>
        /// 禁用最大化按钮
        /// </summary>
        public static void ButtonDisableMaximize()
        {
            DisableMaximizeButton();
        }

        /// <summary>
        /// 启用最小化按钮
        /// </summary>
        public static void ButtonEnableMinimize()
        {
            EnableMinimizeButton();
        }

        /// <summary>
        /// 禁用最小化按钮
        /// </summary>
        public static void ButtonDisableMinimize()
        {
            DisableMinimizeButton();
        }

        /// <summary>
        /// 检查关闭按钮是否启用
        /// </summary>
        public static bool IsButtonCloseEnabled()
        {
            return GetIsCloseButtonEnabled();
        }

        /// <summary>
        /// 检查最大化按钮是否启用
        /// </summary>
        public static bool IsButtonMaximizeEnabled()
        {
            return GetIsMaximizeButtonEnabled();
        }

        /// <summary>
        /// 检查最小化按钮是否启用
        /// </summary>
        public static bool IsButtonMinimizeEnabled()
        {
            return GetIsMinimizeButtonEnabled();
        }

        /// <summary>
        /// 设置窗口位置
        /// </summary>
        /// <param name="position">窗口位置</param>
        public static void SetPosition(Vector2Int position)
        {
            SetWindowPosition(position.x, position.y);
        }

        /// <summary>
        /// 设置窗口大小
        /// </summary>
        /// <param name="size">窗口大小</param>
        public static void SetSize(Vector2Int size)
        {
            SetWindowSize(size.x, size.y);
        }

        /// <summary>
        /// 获取窗口位置
        /// </summary>
        /// <returns>窗口位置</returns>
        public static Vector2Int GetPosition()
        {
            int x, y;
            GetWindowPosition(out x, out y);
            return new Vector2Int(x, y);
        }

        /// <summary>
        /// 获取窗口大小
        /// </summary>
        /// <returns>窗口大小</returns>
        public static Vector2Int GetSize()
        {
            try
            {
                int width = 0, height = 0;
                GetWindowSize(out width, out height);
                return new Vector2Int(width, height);
            }
            catch (EntryPointNotFoundException)
            {
                Debug.LogError("GetWindowSize function not found in WindowsNativePlugin.dll. Using fallback size.");
                return new Vector2Int(Screen.width, Screen.height);
            }
        }

        /// <summary>
        /// 设置窗口标题
        /// </summary>
        /// <param name="title">标题文本</param>
        public static void TitleBarSetTitle(string title)
        {
            SetWindowTitle(title);
        }

        /// <summary>
        /// 获取窗口标题
        /// </summary>
        /// <returns>标题文本</returns>
        public static string TitleBarGetTitle()
        {
            IntPtr titlePtr = GetWindowTitle();
            return Marshal.PtrToStringAnsi(titlePtr);
        }

        /// <summary>
        /// 设置窗口透明度
        /// </summary>
        /// <param name="alpha">透明度值 (0-255)</param>
        public static void SetAlpha(byte alpha)
        {
            SetWindowAlpha(alpha);
        }

        /// <summary>
        /// 检查窗口是否支持透明度
        /// </summary>
        /// <returns>是否支持透明度</returns>
        public static bool HasAlpha()
        {
            return GetWindowHasAlpha();
        }

        #endregion
        
        /// <summary>
        /// 设置窗口为圆角无边框样式
        /// </summary>
        /// <param name="cornerRadius">圆角半径（像素）</param>
        public static void StyleRoundedFrameless(int cornerRadius = 10)
        {
            SetWindowStyleRoundedFrameless(cornerRadius);
        }
        
        /// <summary>
        /// 获取窗口当前的圆角半径
        /// </summary>
        /// <returns>圆角半径（像素）</returns>
        public static int GetCornerRadius()
        {
            return GetWindowCornerRadius();
        }
        
        /// <summary>
        /// 检查窗口是否为圆角无边框样式
        /// </summary>
        /// <returns>如果是圆角无边框样式则返回true</returns>
        public static bool IsRoundedFrameless()
        {
            return GetIsWindowStyleRoundedFrameless();
        }

        // 添加窗口拖动API
        [DllImport("WindowsNativePlugin")]
        private static extern void StartWindowDrag();

        /// <summary>
        /// 开始拖动窗口（使用Windows原生拖动）
        /// </summary>
        public static void StartDrag()
        {
            StartWindowDrag();
        }
    }
}