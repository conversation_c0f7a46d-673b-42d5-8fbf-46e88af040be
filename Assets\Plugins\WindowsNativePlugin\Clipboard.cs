using System;
using System.Runtime.InteropServices;
using UnityEngine;

namespace WindowsNativePlugin
{
    public static class Clipboard
    {
        #region DLL导入

        [DllImport("WindowsNativePlugin")]
        private static extern void SetClipboardText(string text, bool emptyClipboardBeforeSet);

        [DllImport("WindowsNativePlugin")]
        private static extern IntPtr GetClipboardText();

        [DllImport("WindowsNativePlugin")]
        private static extern void SetClipboardImage(IntPtr texturePtr, int width, int height, bool emptyClipboardBeforeSet);

        [DllImport("WindowsNativePlugin")]
        private static extern IntPtr GetClipboardImage(out int width, out int height);

        [DllImport("WindowsNativePlugin")]
        private static extern void ClearClipboard();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetHasClipboardText();

        [DllImport("WindowsNativePlugin")]
        private static extern bool GetHasClipboardImage();

        [DllImport("WindowsNativePlugin")]
        private static extern bool IsFormatAvailable(uint format);

        #endregion

        #region 公共API

        /// <summary>
        /// 设置剪贴板文本
        /// </summary>
        /// <param name="text">要设置的文本</param>
        /// <param name="emptyClipbordBeforeSet">设置前是否清空剪贴板</param>
        public static void SetText(string text, bool emptyClipbordBeforeSet = true)
        {
            SetClipboardText(text, emptyClipbordBeforeSet);
        }

        /// <summary>
        /// 获取剪贴板文本
        /// </summary>
        /// <returns>剪贴板文本</returns>
        public static string GetText()
        {
            IntPtr textPtr = GetClipboardText();
            return Marshal.PtrToStringAnsi(textPtr);
        }

        /// <summary>
        /// 设置剪贴板图像（从Sprite）
        /// </summary>
        /// <param name="sprite">要设置的Sprite</param>
        /// <param name="emptyClipbordBeforeSet">设置前是否清空剪贴板</param>
        public static void SetImage(Sprite sprite, bool emptyClipbordBeforeSet = true)
        {
            if (sprite == null || sprite.texture == null)
                return;

            Texture2D texture = new Texture2D(
                (int)sprite.rect.width,
                (int)sprite.rect.height,
                TextureFormat.RGBA32,
                false);

            Color[] pixels = sprite.texture.GetPixels(
                (int)sprite.rect.x,
                (int)sprite.rect.y,
                (int)sprite.rect.width,
                (int)sprite.rect.height);

            texture.SetPixels(pixels);
            texture.Apply();

            SetImage(texture, emptyClipbordBeforeSet);
        }

        /// <summary>
        /// 设置剪贴板图像（从Texture）
        /// </summary>
        /// <param name="texture">要设置的Texture</param>
        /// <param name="emptyClipbordBeforeSet">设置前是否清空剪贴板</param>
        public static void SetImage(Texture texture, bool emptyClipbordBeforeSet = true)
        {
            if (texture == null)
                return;

            RenderTexture renderTexture = RenderTexture.GetTemporary(
                texture.width,
                texture.height,
                0,
                RenderTextureFormat.ARGB32);

            Graphics.Blit(texture, renderTexture);

            Texture2D texture2D = new Texture2D(
                texture.width,
                texture.height,
                TextureFormat.RGBA32,
                false);

            RenderTexture.active = renderTexture;
            texture2D.ReadPixels(new Rect(0, 0, renderTexture.width, renderTexture.height), 0, 0);
            texture2D.Apply();

            RenderTexture.active = null;
            RenderTexture.ReleaseTemporary(renderTexture);

            SetImage(texture2D, emptyClipbordBeforeSet);
        }

        /// <summary>
        /// 设置剪贴板图像（从Texture2D）
        /// </summary>
        /// <param name="texture2D">要设置的Texture2D</param>
        /// <param name="emptyClipbordBeforeSet">设置前是否清空剪贴板</param>
        public static void SetImage(Texture2D texture2D, bool emptyClipbordBeforeSet = true)
        {
            if (texture2D == null)
                return;

            Color32[] pixels = texture2D.GetPixels32();
            GCHandle handle = GCHandle.Alloc(pixels, GCHandleType.Pinned);
            IntPtr pixelsPtr = handle.AddrOfPinnedObject();

            SetClipboardImage(pixelsPtr, texture2D.width, texture2D.height, emptyClipbordBeforeSet);

            handle.Free();
        }

        /// <summary>
        /// 获取剪贴板图像
        /// </summary>
        /// <returns>剪贴板图像</returns>
        public static Texture2D GetImage()
        {
            int width, height;
            IntPtr pixelsPtr = GetClipboardImage(out width, out height);

            if (pixelsPtr == IntPtr.Zero || width <= 0 || height <= 0)
                return null;

            // 计算总字节数
            int byteCount = width * height * 4; // RGBA32 每个像素占 4 字节
            byte[] bytes = new byte[byteCount];

            // 将指针指向的数据复制到 byte 数组中
            Marshal.Copy(pixelsPtr, bytes, 0, byteCount);

            // 将 byte 数组转换为 Color32 数组
            Color32[] pixels = new Color32[width * height];
            for (int i = 0; i < pixels.Length; i++)
            {
                int index = i * 4;
                pixels[i] = new Color32(bytes[index], bytes[index + 1], bytes[index + 2], bytes[index + 3]);
            }

            // 创建并设置纹理
            Texture2D texture = new Texture2D(width, height, TextureFormat.RGBA32, false);
            texture.SetPixels32(pixels);
            texture.Apply();

            return texture;
        }

        /// <summary>
        /// 清空剪贴板
        /// </summary>
        public static void Clear()
        {
            ClearClipboard();
        }

        /// <summary>
        /// 检查剪贴板是否包含文本
        /// </summary>
        /// <returns>是否包含文本</returns>
        public static bool HasText()
        {
            return GetHasClipboardText();
        }

        /// <summary>
        /// 检查剪贴板是否包含图像
        /// </summary>
        /// <returns>是否包含图像</returns>
        public static bool HasImage()
        {
            return GetHasClipboardImage();
        }

        /// <summary>
        /// 检查剪贴板是否支持指定格式
        /// </summary>
        /// <param name="format">格式代码</param>
        /// <returns>是否支持该格式</returns>
        public static bool IsClipboardFormatAvailable(uint format)
        {
            return IsFormatAvailable(format);
        }

        #endregion
    }
}