using System.Runtime.InteropServices;
using UnityEngine;

namespace WindowsNativePlugin
{
    public enum ProgressStyle
    {
        None = 0,
        Normal = 1,
        Error = 2,
        Paused = 3,
        Indeterminate = 4
    }

    public static class Taskbar
    {
        #region DLL导入

        [DllImport("WindowsNativePlugin")]
        private static extern void SetTaskbarProgressValue(int progressValue, int progressMax, int style);

        [DllImport("WindowsNativePlugin")]
        private static extern void SetTaskbarProgressStyle(int style);

        [DllImport("WindowsNativePlugin")]
        private static extern void HideTaskbarProgress();

        [DllImport("WindowsNativePlugin")]
        private static extern void FlashTaskbarOnce();

        [DllImport("WindowsNativePlugin")]
        private static extern void FlashTaskbarUntilFocus();

        [DllImport("WindowsNativePlugin")]
        private static extern void FlashTaskbarStart();

        [DllImport("WindowsNativePlugin")]
        private static extern void FlashTaskbarStop();

        [DllImport("WindowsNativePlugin")]
        private static extern void FlashTaskbarCount(int count);

        #endregion

        #region 公共API

        /// <summary>
        /// 设置任务栏进度值
        /// </summary>
        /// <param name="progressValue">当前进度值</param>
        /// <param name="progressMax">最大进度值</param>
        /// <param name="style">进度样式</param>
        public static void SetProgressValue(int progressValue, int progressMax, ProgressStyle style = ProgressStyle.Normal)
        {
            SetTaskbarProgressValue(progressValue, progressMax, (int)style);
        }

        /// <summary>
        /// 设置任务栏进度样式
        /// </summary>
        /// <param name="style">进度样式</param>
        public static void SetProgressStyle(ProgressStyle style)
        {
            SetTaskbarProgressStyle((int)style);
        }

        /// <summary>
        /// 隐藏任务栏进度
        /// </summary>
        public static void HideProgress()
        {
            HideTaskbarProgress();
        }

        /// <summary>
        /// 任务栏闪烁一次
        /// </summary>
        public static void FlashOnce()
        {
            FlashTaskbarOnce();
        }

        /// <summary>
        /// 任务栏闪烁直到获得焦点
        /// </summary>
        public static void FlashUntilFocus()
        {
            FlashTaskbarUntilFocus();
        }

        /// <summary>
        /// 开始任务栏闪烁
        /// </summary>
        public static void FlashStart()
        {
            FlashTaskbarStart();
        }

        /// <summary>
        /// 停止任务栏闪烁
        /// </summary>
        public static void FlashStop()
        {
            FlashTaskbarStop();
        }

        /// <summary>
        /// 任务栏闪烁指定次数
        /// </summary>
        /// <param name="count">闪烁次数</param>
        public static void FlashCount(int count)
        {
            FlashTaskbarCount(count);
        }

        #endregion
    }
}