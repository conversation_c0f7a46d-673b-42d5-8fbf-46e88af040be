using System.Runtime.InteropServices;
using UnityEngine;

namespace WindowsNativePlugin
{
    public static class Cursor
    {
        #region DLL导入

        [DllImport("WindowsNativePlugin")]
        private static extern void GetCursorPosition(out int x, out int y);

        [DllImport("WindowsNativePlugin")]
        private static extern void GetPhysicalCursorPosition(out int x, out int y);

        [DllImport("WindowsNativePlugin")]
        private static extern void SetCursorPosition(int x, int y);

        [DllImport("WindowsNativePlugin")]
        private static extern void SetPhysicalCursorPosition(int x, int y);

        #endregion

        #region 公共API

        /// <summary>
        /// 获取光标位置（屏幕坐标）
        /// </summary>
        /// <returns>光标位置</returns>
        public static Vector2Int GetCursorPos()
        {
            int x, y;
            GetCursorPosition(out x, out y);
            return new Vector2Int(x, y);
        }

        /// <summary>
        /// 获取光标物理位置
        /// </summary>
        /// <returns>光标物理位置</returns>
        public static Vector2Int GetPhysicalCursorPos()
        {
            int x, y;
            GetPhysicalCursorPosition(out x, out y);
            return new Vector2Int(x, y);
        }

        /// <summary>
        /// 设置光标位置（屏幕坐标）
        /// </summary>
        /// <param name="position">光标位置</param>
        public static void SetCursorPos(Vector2Int position)
        {
            SetCursorPosition(position.x, position.y);
        }

        /// <summary>
        /// 设置光标物理位置
        /// </summary>
        /// <param name="position">光标物理位置</param>
        public static void SetPhysicalCursorPos(Vector2Int position)
        {
            SetPhysicalCursorPosition(position.x, position.y);
        }

        #endregion
    }
}