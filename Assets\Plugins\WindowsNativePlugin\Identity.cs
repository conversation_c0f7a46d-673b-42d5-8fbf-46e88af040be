using System;
using System.Runtime.InteropServices;
using UnityEngine;

namespace WindowsNativePlugin
{
    public static class Identity
    {
        // Use the correct DLL name based on architecture
#if UNITY_EDITOR_64 || UNITY_STANDALONE_WIN_64
        private const string DllName = "WindowsNativePlugin";
#else
            private const string DllName = "WindowsNativePlugin";
#endif

        [DllImport(DllName)]
        private static extern System.IntPtr GetUserNameNative();

        [DllImport(DllName)]
        private static extern System.IntPtr GetDomainNameNative();

        [DllImport(DllName)]
        private static extern System.IntPtr GetMachineNameNative();

        [DllImport(DllName)]
        private static extern System.IntPtr GetDeviceUniqueIdentifierNative();

        public static string GetUserName()
        {
            return Marshal.PtrToStringAnsi(GetUserNameNative());
        }

        public static string GetDomainName()
        {
            return Marshal.PtrToStringAnsi(GetDomainNameNative());
        }

        public static string GetMachineName()
        {
            return Marshal.PtrToStringAnsi(GetMachineNameNative());
        }

        public static string GetDeviceUniqueIdentifier()
        {
            return Marshal.PtrToStringAnsi(GetDeviceUniqueIdentifierNative());
        }
    }
}