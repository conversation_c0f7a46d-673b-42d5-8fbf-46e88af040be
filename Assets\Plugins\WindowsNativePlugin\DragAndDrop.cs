using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;
using UnityEngine.Events;

namespace WindowsNativePlugin
{
    [Serializable]
    public class DroppedFilesEvent : UnityEvent<List<string>> { }

    public static class DragAndDrop
    {
        #region DLL导入

        [DllImport("WindowsNativePlugin")]
        private static extern void EnableDragAndDrop();

        [DllImport("WindowsNativePlugin")]
        private static extern void DisableDragAndDrop();

        [DllImport("WindowsNativePlugin")]
        private static extern void RegisterDropCallback(Action<IntPtr, int> callback);

        #endregion

        #region 事件

        public static DroppedFilesEvent OnDroppedFiles = new DroppedFilesEvent();

        #endregion

        #region 私有字段

        private static bool isInitialized = false;
        private static Action<IntPtr, int> dropCallback;

        #endregion

        #region 公共API

        /// <summary>
        /// 启用拖放功能
        /// </summary>
        public static void Enable()
        {
            if (!isInitialized)
            {
                Initialize();
            }
            EnableDragAndDrop();
        }

        /// <summary>
        /// 禁用拖放功能
        /// </summary>
        public static void Disable()
        {
            DisableDragAndDrop();
        }

        #endregion

        #region 私有方法

        private static void Initialize()
        {
            dropCallback = new Action<IntPtr, int>(OnFilesDropped);
            RegisterDropCallback(dropCallback);
            isInitialized = true;
        }

        private static void OnFilesDropped(IntPtr filesPtr, int count)
        {
            List<string> files = new List<string>();

            for (int i = 0; i < count; i++)
            {
                IntPtr strPtr = Marshal.ReadIntPtr(filesPtr, i * IntPtr.Size);
                string file = Marshal.PtrToStringAnsi(strPtr);
                files.Add(file);
            }

            if (OnDroppedFiles != null)
            {
                OnDroppedFiles.Invoke(files);
            }
        }

        #endregion
    }

    public class DragAndDropArea : MonoBehaviour
    {
        public DroppedFilesEvent OnDroppedFilesInArea = new DroppedFilesEvent();

        private void Awake()
        {
            DragAndDrop.OnDroppedFiles.AddListener(OnFilesDropped);
        }

        private void OnDestroy()
        {
            DragAndDrop.OnDroppedFiles.RemoveListener(OnFilesDropped);
        }

        private void OnFilesDropped(List<string> files)
        {
            // 检查拖放是否在此区域内
            if (IsPointerOverGameObject())
            {
                OnDroppedFilesInArea.Invoke(files);
            }
        }

        private bool IsPointerOverGameObject()
        {
            // 获取当前鼠标位置
            Vector2 mousePosition = Input.mousePosition;

            // 检查鼠标是否在此UI元素上
            RectTransform rectTransform = GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                return RectTransformUtility.RectangleContainsScreenPoint(rectTransform, mousePosition);
            }

            return false;
        }
    }
}